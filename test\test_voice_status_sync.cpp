/**
 * @file test_voice_status_sync.cpp
 * @brief Test voice manager status synchronization with voice player
 */

#include <iostream>
#include <cassert>

// Mock voice player status
enum voice_player_status_t {
    VOICE_PLAYER_IDLE = 0,
    VOICE_PLAYER_PLAYING,
    VOICE_PLAYER_ERROR
};

// Mock voice manager status
enum voice_status_t {
    VOICE_STATUS_UNINITIALIZED = 0,
    VOICE_STATUS_INITIALIZED,
    VOICE_STATUS_PLAYING,
    VOICE_STATUS_ERROR
};

// Global mock variables
static voice_player_status_t g_player_status = VOICE_PLAYER_IDLE;
static voice_status_t g_manager_status = VOICE_STATUS_INITIALIZED;
static bool g_audio_running = false;

// Mock functions
bool voice_player_is_playing(void) {
    return g_player_status == VOICE_PLAYER_PLAYING && g_audio_running;
}

void voice_player_process(void) {
    // Simulate audio completion
    if (g_player_status == VOICE_PLAYER_PLAYING && !g_audio_running) {
        std::cout << "Voice player: playback completed" << std::endl;
        g_player_status = VOICE_PLAYER_IDLE;
    }
}

// Voice manager functions (simplified)
void voice_manager_process(void) {
    if (g_manager_status == VOICE_STATUS_UNINITIALIZED) {
        return;
    }

    // Sync status with voice player
    if (g_manager_status == VOICE_STATUS_PLAYING && !voice_player_is_playing()) {
        std::cout << "Voice manager: playback completed, updating status" << std::endl;
        g_manager_status = VOICE_STATUS_INITIALIZED;
    }
}

bool voice_manager_is_playing(void) {
    return voice_player_is_playing();
}

voice_status_t voice_manager_get_status(void) {
    return g_manager_status;
}

// Simulate voice system process
void voice_system_process(void) {
    voice_player_process();
    voice_manager_process();
}

// Test functions
void test_status_sync_on_completion() {
    std::cout << "\n=== Testing Status Sync on Completion ===" << std::endl;
    
    // Initial state
    g_manager_status = VOICE_STATUS_INITIALIZED;
    g_player_status = VOICE_PLAYER_IDLE;
    g_audio_running = false;
    
    std::cout << "Initial - Manager: " << g_manager_status << ", Player playing: " << voice_player_is_playing() << std::endl;
    
    // Start playback
    g_manager_status = VOICE_STATUS_PLAYING;
    g_player_status = VOICE_PLAYER_PLAYING;
    g_audio_running = true;
    
    std::cout << "Started - Manager: " << g_manager_status << ", Player playing: " << voice_player_is_playing() << std::endl;
    assert(g_manager_status == VOICE_STATUS_PLAYING);
    assert(voice_player_is_playing() == true);
    
    // Simulate audio completion
    g_audio_running = false;
    
    std::cout << "Audio stopped - Manager: " << g_manager_status << ", Player playing: " << voice_player_is_playing() << std::endl;
    
    // Process should sync the status
    voice_system_process();
    
    std::cout << "After process - Manager: " << g_manager_status << ", Player playing: " << voice_player_is_playing() << std::endl;
    assert(g_manager_status == VOICE_STATUS_INITIALIZED);
    assert(voice_player_is_playing() == false);
    
    std::cout << "✓ Status sync on completion works correctly" << std::endl;
}

void test_status_consistency() {
    std::cout << "\n=== Testing Status Consistency ===" << std::endl;
    
    // Reset state
    g_manager_status = VOICE_STATUS_INITIALIZED;
    g_player_status = VOICE_PLAYER_IDLE;
    g_audio_running = false;
    
    // Test multiple process calls when idle
    for (int i = 0; i < 5; i++) {
        voice_system_process();
        assert(g_manager_status == VOICE_STATUS_INITIALIZED);
        assert(voice_player_is_playing() == false);
    }
    
    std::cout << "✓ Status remains consistent when idle" << std::endl;
    
    // Test during playback
    g_manager_status = VOICE_STATUS_PLAYING;
    g_player_status = VOICE_PLAYER_PLAYING;
    g_audio_running = true;
    
    for (int i = 0; i < 3; i++) {
        voice_system_process();
        assert(g_manager_status == VOICE_STATUS_PLAYING);
        assert(voice_player_is_playing() == true);
    }
    
    std::cout << "✓ Status remains consistent during playback" << std::endl;
    
    // Complete playback
    g_audio_running = false;
    voice_system_process();
    
    assert(g_manager_status == VOICE_STATUS_INITIALIZED);
    assert(voice_player_is_playing() == false);
    
    std::cout << "✓ Status sync works after playback completion" << std::endl;
}

void test_edge_cases() {
    std::cout << "\n=== Testing Edge Cases ===" << std::endl;
    
    // Test uninitialized state
    g_manager_status = VOICE_STATUS_UNINITIALIZED;
    voice_manager_process();
    assert(g_manager_status == VOICE_STATUS_UNINITIALIZED);
    std::cout << "✓ Uninitialized state handled correctly" << std::endl;
    
    // Test error state
    g_manager_status = VOICE_STATUS_ERROR;
    g_player_status = VOICE_PLAYER_ERROR;
    g_audio_running = false;
    voice_manager_process();
    // Error state should not change automatically
    assert(g_manager_status == VOICE_STATUS_ERROR);
    std::cout << "✓ Error state preserved" << std::endl;
    
    // Test player error during playback
    g_manager_status = VOICE_STATUS_PLAYING;
    g_player_status = VOICE_PLAYER_ERROR;
    g_audio_running = false;
    voice_manager_process();
    // Should sync to initialized when player is not playing
    assert(g_manager_status == VOICE_STATUS_INITIALIZED);
    std::cout << "✓ Player error handled correctly" << std::endl;
}

void test_rapid_state_changes() {
    std::cout << "\n=== Testing Rapid State Changes ===" << std::endl;
    
    // Simulate rapid start/stop cycles
    for (int cycle = 0; cycle < 3; cycle++) {
        std::cout << "Cycle " << (cycle + 1) << std::endl;
        
        // Start
        g_manager_status = VOICE_STATUS_PLAYING;
        g_player_status = VOICE_PLAYER_PLAYING;
        g_audio_running = true;
        
        voice_system_process();
        assert(g_manager_status == VOICE_STATUS_PLAYING);
        
        // Stop
        g_audio_running = false;
        voice_system_process();
        assert(g_manager_status == VOICE_STATUS_INITIALIZED);
        
        // Process again (should remain stable)
        voice_system_process();
        assert(g_manager_status == VOICE_STATUS_INITIALIZED);
    }
    
    std::cout << "✓ Rapid state changes handled correctly" << std::endl;
}

int main() {
    std::cout << "Starting Voice Status Sync Tests..." << std::endl;
    
    try {
        test_status_sync_on_completion();
        test_status_consistency();
        test_edge_cases();
        test_rapid_state_changes();
        
        std::cout << "\n🎉 All voice status sync tests passed!" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cout << "\n❌ Test failed: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cout << "\n❌ Test failed with unknown error" << std::endl;
        return 1;
    }
}
