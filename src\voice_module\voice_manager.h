#ifndef VOICE_MANAGER_H
#define VOICE_MANAGER_H

#include <Arduino.h>
#include <Audio.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C"
{
#endif

    // Voice file indices - must match the order in voice_files folder
    typedef enum
    {
        VOICE_OPEN_APP_TO_CONFIG = 0,
        VOICE_NETWORK_SUCCESS,
        VOICE_SELECT_USER,
        VOICE_BLOOD_PRESSURE_DATA,
        VOICE_TEMPERATURE_DATA,
        VOICE_WEIGHT_DATA,
        VOICE_BLOOD_GLUCOSE_DATA,
        VOICE_BLOOD_OXYGEN_DATA,
        VOICE_TAP_SMART_CONFIG,
        VOICE_NEW_MESSAGE,
        VOICE_FILE_COUNT
    } voice_file_index_t;

    // Voice file structure for embedded data
    typedef struct
    {
        const char *name;    // File name without extension
        const uint8_t *data; // Pointer to embedded binary data
        size_t size;         // Size of the embedded data
        bool available;      // Whether the file is available
    } voice_file_t;

    // Voice manager status
    typedef enum
    {
        VOICE_STATUS_UNINITIALIZED = 0,
        VOICE_STATUS_INITIALIZED,
        VOICE_STATUS_PLAYING,
        VOICE_STATUS_ERROR
    } voice_status_t;

    // Error codes
    typedef enum
    {
        VOICE_ERROR_NONE = 0,
        VOICE_ERROR_NOT_INITIALIZED,
        VOICE_ERROR_INVALID_INDEX,
        VOICE_ERROR_FILE_NOT_FOUND,
        VOICE_ERROR_MEMORY_ERROR,
        VOICE_ERROR_AUDIO_BUSY,
        VOICE_ERROR_INVALID_DATA
    } voice_error_t;

    /**
     * @brief Initialize the voice manager system
     *
     * This function initializes the embedded voice files and sets up
     * the audio system for playback from memory.
     *
     * @return voice_error_t Error code (VOICE_ERROR_NONE on success)
     */
    voice_error_t voice_manager_init(void);

    /**
     * @brief Set the audio instance for voice playback
     *
     * @param audio_instance Pointer to the Audio instance
     * @return voice_error_t Error code (VOICE_ERROR_NONE on success)
     */
    voice_error_t voice_manager_set_audio_instance(Audio *audio_instance);

    /**
     * @brief Deinitialize the voice manager system
     *
     * Cleans up resources and stops any ongoing playback.
     */
    void voice_manager_deinit(void);

    /**
     * @brief Play a voice file by index
     *
     * @param index Voice file index from voice_file_index_t enum
     * @return voice_error_t Error code (VOICE_ERROR_NONE on success)
     */
    voice_error_t voice_manager_play(voice_file_index_t index);

    /**
     * @brief Play a voice file by name
     *
     * @param filename Voice file name (without .wav extension)
     * @return voice_error_t Error code (VOICE_ERROR_NONE on success)
     */
    voice_error_t voice_manager_play_by_name(const char *filename);

    /**
     * @brief Stop current voice playback
     *
     * @return voice_error_t Error code (VOICE_ERROR_NONE on success)
     */
    voice_error_t voice_manager_stop(void);

    /**
     * @brief Check if voice manager is currently playing
     *
     * @return true if playing, false otherwise
     */
    bool voice_manager_is_playing(void);

    /**
     * @brief Process voice manager (call in main loop)
     *
     * This function should be called regularly to sync status with voice player.
     */
    void voice_manager_process(void);

    /**
     * @brief Get current voice manager status
     *
     * @return voice_status_t Current status
     */
    voice_status_t voice_manager_get_status(void);

    /**
     * @brief Check if a voice file is available
     *
     * @param index Voice file index
     * @return true if available, false otherwise
     */
    bool voice_manager_is_file_available(voice_file_index_t index);

    /**
     * @brief Get voice file information
     *
     * @param index Voice file index
     * @return const voice_file_t* Pointer to voice file info (NULL if invalid)
     */
    const voice_file_t *voice_manager_get_file_info(voice_file_index_t index);

    /**
     * @brief Get total number of embedded voice files
     *
     * @return uint8_t Number of voice files
     */
    uint8_t voice_manager_get_file_count(void);

    /**
     * @brief Convert error code to string
     *
     * @param error Error code
     * @return const char* Error description string
     */
    const char *voice_manager_error_to_string(voice_error_t error);

#ifdef __cplusplus
}
#endif

#endif // VOICE_MANAGER_H
